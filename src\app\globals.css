@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Orbitron:wght@400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 15, 23, 42;
  --background-end-rgb: 2, 6, 23;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  font-family: 'Inter', sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #00d4ff;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #0ea5e9;
}

/* Glassmorphism utility classes */
@layer utilities {
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  .glass-dark {
    @apply bg-black/20 backdrop-blur-md border border-white/10;
  }
  
  .text-glow {
    text-shadow: 0 0 10px currentColor;
  }
  
  .neon-border {
    box-shadow: 0 0 5px #00d4ff, 0 0 10px #00d4ff, 0 0 15px #00d4ff;
  }
  
  .hover-glow {
    transition: all 0.3s ease;
  }
  
  .hover-glow:hover {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
    transform: translateY(-2px);
  }
}

/* Animation classes */
@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-neon-blue to-primary-500 text-white font-semibold py-3 px-6 rounded-lg hover-glow transition-all duration-300 hover:scale-105;
  }
  
  .btn-secondary {
    @apply glass text-white font-semibold py-3 px-6 rounded-lg hover-glow transition-all duration-300 hover:scale-105;
  }
  
  .card {
    @apply glass-dark rounded-xl p-6 hover-glow transition-all duration-300;
  }
  
  .section-padding {
    @apply py-16 px-4 sm:px-6 lg:px-8;
  }
}
