'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { ArrowRight, Play, Cpu, Zap, Cog } from 'lucide-react'

const Hero = () => {
  const [currentText, setCurrentText] = useState(0)
  
  const heroTexts = [
    "Ideate, Innovate, Inspire! ⚡",
    "Where Code Meets Creativity 🤖",
    "Building the Future, One Bot at a Time 🚀",
    "Robocon Champions & Tech Innovators 🏆"
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentText((prev) => (prev + 1) % heroTexts.length)
    }, 3000)
    return () => clearInterval(interval)
  }, [])

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-dark-800 via-dark-900 to-dark-950">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-circuit-pattern opacity-10" />
      <div className="absolute inset-0 bg-gradient-to-br from-robocell-yellow/5 via-transparent to-robocell-orange/5" />
      
      {/* Floating Elements */}
      <div className="absolute top-20 left-10 animate-float">
        <Cpu className="h-12 w-12 text-robocell-yellow opacity-20" />
      </div>
      <div className="absolute top-40 right-20 animate-float" style={{ animationDelay: '1s' }}>
        <Cog className="h-16 w-16 text-robocell-orange opacity-15 animate-spin-slow" />
      </div>
      <div className="absolute bottom-40 left-20 animate-float" style={{ animationDelay: '2s' }}>
        <Zap className="h-10 w-10 text-electric-yellow opacity-30" />
      </div>
      <div className="absolute top-1/3 right-1/4 animate-float" style={{ animationDelay: '3s' }}>
        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-robocell-yellow to-robocell-orange opacity-20" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="space-y-8">
          {/* Main Heading */}
          <div className="space-y-4">
            <h1 className="font-tech text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight">
              <span className="block bg-gradient-to-r from-robocell-yellow via-robocell-orange to-electric-amber bg-clip-text text-transparent">
                RoboCell
              </span>
              <span className="block text-2xl md:text-3xl lg:text-4xl font-normal text-gray-300 mt-2">
                NIT Durgapur
              </span>
            </h1>
            
            {/* Animated Tagline */}
            <div className="h-16 md:h-20 flex items-center justify-center">
              <h2 className="font-tech text-xl md:text-3xl lg:text-4xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-electric-yellow via-robocell-orange to-electric-amber animate-pulse">
                {heroTexts[currentText]}
              </h2>
            </div>
          </div>

          {/* Description */}
          <p className="max-w-3xl mx-auto text-lg md:text-xl text-gray-300 leading-relaxed">
            🤖 Welcome to RoboCell - where engineering students turn wild ideas into reality!
            Join us for epic robotics projects, Robocon adventures, and mind-blowing tech innovations.
            Ready to code, build, and conquer? Let's make some robot magic! ✨
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link href="/projects" className="btn-primary group">
              Explore Projects
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Link>
            <Link href="/join" className="btn-secondary group">
              <Play className="mr-2 h-5 w-5" />
              Join the Mission
            </Link>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16">
            <div className="text-center group">
              <div className="font-tech text-3xl md:text-4xl font-bold text-robocell-yellow group-hover:scale-110 transition-transform duration-300">587+</div>
              <div className="text-gray-400 mt-1">Instagram Followers</div>
            </div>
            <div className="text-center group">
              <div className="font-tech text-3xl md:text-4xl font-bold text-robocell-orange group-hover:scale-110 transition-transform duration-300">30+</div>
              <div className="text-gray-400 mt-1">Active Projects</div>
            </div>
            <div className="text-center group">
              <div className="font-tech text-3xl md:text-4xl font-bold text-electric-amber group-hover:scale-110 transition-transform duration-300">2025</div>
              <div className="text-gray-400 mt-1">Robocon Ready</div>
            </div>
            <div className="text-center group">
              <div className="font-tech text-3xl md:text-4xl font-bold text-electric-yellow group-hover:scale-110 transition-transform duration-300">CCA</div>
              <div className="text-gray-400 mt-1">Oldest Tech Club</div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-robocell-yellow rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gradient-to-b from-robocell-yellow to-robocell-orange rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </section>
  )
}

export default Hero
