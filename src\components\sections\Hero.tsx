'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { ArrowRight, Play, <PERSON><PERSON>, Zap, Cog } from 'lucide-react'

const Hero = () => {
  const [currentText, setCurrentText] = useState(0)
  
  const heroTexts = [
    "Ideate, Innovate, Inspire!",
    "The Heart of Robotics at NIT Durgapur",
    "Building Tomorrow's Technology Today",
    "Robocon Champions & Innovators"
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentText((prev) => (prev + 1) % heroTexts.length)
    }, 3000)
    return () => clearInterval(interval)
  }, [])

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-circuit-pattern opacity-20" />
      <div className="absolute inset-0 bg-gradient-to-br from-dark-900/50 via-transparent to-dark-950/50" />
      
      {/* Floating Elements */}
      <div className="absolute top-20 left-10 animate-float">
        <Cpu className="h-12 w-12 text-neon-blue opacity-30" />
      </div>
      <div className="absolute top-40 right-20 animate-float" style={{ animationDelay: '1s' }}>
        <Cog className="h-16 w-16 text-neon-green opacity-20 animate-spin-slow" />
      </div>
      <div className="absolute bottom-40 left-20 animate-float" style={{ animationDelay: '2s' }}>
        <Zap className="h-10 w-10 text-neon-purple opacity-40" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="space-y-8">
          {/* Main Heading */}
          <div className="space-y-4">
            <h1 className="font-tech text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight">
              <span className="block text-neon-blue text-glow">RoboCell</span>
              <span className="block text-2xl md:text-3xl lg:text-4xl font-normal text-gray-300 mt-2">
                NIT Durgapur
              </span>
            </h1>
            
            {/* Animated Tagline */}
            <div className="h-16 md:h-20 flex items-center justify-center">
              <h2 className="font-tech text-xl md:text-3xl lg:text-4xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-neon-blue via-neon-green to-neon-purple animate-pulse">
                {heroTexts[currentText]}
              </h2>
            </div>
          </div>

          {/* Description */}
          <p className="max-w-3xl mx-auto text-lg md:text-xl text-gray-300 leading-relaxed">
            Welcome to RoboCell, the official robotics club under the Centre for Cognitive Activities (CCA),
            NIT Durgapur. We focus on cutting-edge robotics projects, research work, and innovation.
            From Robocon championships to advanced automation projects, we're building the future!
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link href="/projects" className="btn-primary group">
              Explore Projects
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Link>
            <Link href="/join" className="btn-secondary group">
              <Play className="mr-2 h-5 w-5" />
              Join the Mission
            </Link>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16">
            <div className="text-center">
              <div className="font-tech text-3xl md:text-4xl font-bold text-neon-blue">587+</div>
              <div className="text-gray-400 mt-1">Instagram Followers</div>
            </div>
            <div className="text-center">
              <div className="font-tech text-3xl md:text-4xl font-bold text-neon-green">30+</div>
              <div className="text-gray-400 mt-1">Active Projects</div>
            </div>
            <div className="text-center">
              <div className="font-tech text-3xl md:text-4xl font-bold text-neon-purple">2025</div>
              <div className="text-gray-400 mt-1">Robocon Ready</div>
            </div>
            <div className="text-center">
              <div className="font-tech text-3xl md:text-4xl font-bold text-neon-orange">CCA</div>
              <div className="text-gray-400 mt-1">Oldest Tech Club</div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-neon-blue rounded-full flex justify-center">
          <div className="w-1 h-3 bg-neon-blue rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </section>
  )
}

export default Hero
