{"name": "robocell-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5", "tailwindcss": "^3.3.0", "autoprefixer": "^10.0.1", "postcss": "^8", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "react-intersection-observer": "^9.5.3", "lottie-react": "^2.4.0", "@headlessui/react": "^1.7.17", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.0.4", "@tailwindcss/typography": "^0.5.10"}}