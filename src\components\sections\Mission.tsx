import { Eye, Target, Heart } from 'lucide-react'

const Mission = () => {
  return (
    <section className="section-padding bg-dark-800/50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="font-tech text-3xl md:text-5xl font-bold text-white mb-6">
            Our <span className="text-neon-blue">Purpose</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Driven by passion, guided by innovation, and committed to excellence in robotics and automation.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Mission */}
          <div className="card text-center group">
            <div className="relative mb-6">
              <Target className="h-16 w-16 text-neon-blue mx-auto group-hover:animate-pulse" />
              <div className="absolute inset-0 h-16 w-16 text-neon-blue opacity-30 mx-auto group-hover:animate-ping" />
            </div>
            <h3 className="font-tech text-2xl font-bold text-white mb-4">Mission</h3>
            <p className="text-gray-300 leading-relaxed">
              To foster innovation in robotics and embedded systems by providing a collaborative 
              platform for students to learn, create, and implement cutting-edge technological 
              solutions that address real-world challenges.
            </p>
          </div>

          {/* Vision */}
          <div className="card text-center group">
            <div className="relative mb-6">
              <Eye className="h-16 w-16 text-neon-green mx-auto group-hover:animate-pulse" />
              <div className="absolute inset-0 h-16 w-16 text-neon-green opacity-30 mx-auto group-hover:animate-ping" />
            </div>
            <h3 className="font-tech text-2xl font-bold text-white mb-4">Vision</h3>
            <p className="text-gray-300 leading-relaxed">
              To become the leading robotics community in Eastern India, producing skilled 
              engineers who will shape the future of automation and contribute to India's 
              technological advancement on a global scale.
            </p>
          </div>

          {/* Values */}
          <div className="card text-center group">
            <div className="relative mb-6">
              <Heart className="h-16 w-16 text-neon-purple mx-auto group-hover:animate-pulse" />
              <div className="absolute inset-0 h-16 w-16 text-neon-purple opacity-30 mx-auto group-hover:animate-ping" />
            </div>
            <h3 className="font-tech text-2xl font-bold text-white mb-4">Values</h3>
            <p className="text-gray-300 leading-relaxed">
              Innovation, collaboration, excellence, and continuous learning form the core of 
              our community. We believe in sharing knowledge, supporting each other, and 
              pushing the boundaries of what's possible.
            </p>
          </div>
        </div>

        {/* Connection to CCA */}
        <div className="mt-16 card">
          <div className="text-center">
            <h3 className="font-tech text-2xl font-bold text-white mb-4">
              Part of Centre for Cognitive Activities (CCA)
            </h3>
            <p className="text-gray-300 leading-relaxed max-w-4xl mx-auto">
              As a proud member of the Centre for Cognitive Activities at NIT Durgapur, RoboCell 
              benefits from the rich ecosystem of innovation and research. CCA provides us with 
              the infrastructure, mentorship, and collaborative environment necessary to pursue 
              ambitious projects and organize impactful events like our flagship Robozido workshops.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Mission
