# RoboCell Website - NIT Durgapur

A modern, innovative website for RoboCell, the robotics and embedded systems club under the Centre for Cognitive Activities (CCA), NIT Durgapur.

## 🚀 Features

- **Modern Design**: Dark theme with neon accents and glassmorphism effects
- **Responsive**: Mobile-first design that works on all devices
- **Interactive**: Smooth animations and hover effects
- **Fast**: Built with Next.js 14 and optimized for performance
- **Accessible**: WCAG compliant with proper semantic HTML

## 🛠️ Tech Stack

- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS with custom design system
- **Typography**: Inter (body) + Orbitron (headings)
- **Icons**: Lucide React
- **Animations**: Framer Motion + CSS animations
- **Language**: TypeScript

## 📋 Prerequisites

Before you begin, ensure you have the following installed:
- Node.js (version 18 or higher)
- npm, yarn, or pnpm

## 🚀 Getting Started

1. **Install Node.js** (if not already installed):
   - Download from [nodejs.org](https://nodejs.org/)
   - Choose the LTS version for stability

2. **Install dependencies**:
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Run the development server**:
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. **Open your browser** and navigate to [http://localhost:3000](http://localhost:3000)

## 🎨 Design System

### Colors
- **Primary**: Blue (#0ea5e9) - Main brand color
- **Neon Accents**: 
  - Blue (#00d4ff)
  - Green (#00ff88)
  - Purple (#8b5cf6)
  - Pink (#f472b6)
  - Orange (#fb923c)
- **Dark Theme**: Various shades of slate/gray

### Typography
- **Headings**: Orbitron (tech/sci-fi feel)
- **Body**: Inter (clean, readable)

### Components
- Glassmorphism cards
- Neon glow effects
- Smooth hover animations
- Responsive navigation

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   ├── page.tsx          # Home page
│   ├── about/            # About page
│   ├── projects/         # Projects page
│   ├── events/           # Events page
│   ├── team/             # Team page
│   ├── join/             # Join Us page
│   ├── blog/             # Blog pages
│   └── contact/          # Contact page
├── components/
│   ├── layout/           # Layout components
│   ├── sections/         # Page sections
│   ├── ui/              # Reusable UI components
│   └── forms/           # Form components
└── lib/                 # Utility functions
```

## 🔧 Development

### Adding New Pages
1. Create a new folder in `src/app/`
2. Add a `page.tsx` file
3. Update navigation in `Header.tsx`

### Customizing Styles
- Edit `tailwind.config.ts` for theme changes
- Modify `globals.css` for global styles
- Use Tailwind classes for component styling

### Adding Components
- Create in appropriate `components/` subfolder
- Follow TypeScript conventions
- Use Tailwind for styling

## 🚀 Deployment

### Vercel (Recommended)
1. Push code to GitHub
2. Connect repository to Vercel
3. Deploy automatically

### Other Platforms
```bash
npm run build
npm start
```

## 📱 Pages Overview

1. **Home**: Hero section with club introduction
2. **About**: Club history, mission, achievements
3. **Projects**: Showcase of robotics projects
4. **Events**: Timeline of Robozido and other events
5. **Team**: Member profiles and roles
6. **Join Us**: Recruitment information and form
7. **Blog**: Technical articles and tutorials
8. **Contact**: Contact information and form

## 🎯 Next Steps

1. Install Node.js if not already installed
2. Run `npm install` to install dependencies
3. Start development server with `npm run dev`
4. Begin customizing content and adding real data
5. Add actual images and content
6. Set up backend for forms (if needed)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

---

**Built with ❤️ by RoboCell Team, NIT Durgapur**
