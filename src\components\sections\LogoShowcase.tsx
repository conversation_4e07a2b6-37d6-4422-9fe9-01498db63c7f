'use client'

import { <PERSON><PERSON>, Zap, Cpu, Cog } from 'lucide-react'

const LogoShowcase = () => {
  return (
    <section className="section-padding bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 animate-float">
          <Cpu className="h-8 w-8 text-robocell-yellow" />
        </div>
        <div className="absolute top-40 right-20 animate-float" style={{ animationDelay: '1s' }}>
          <Cog className="h-12 w-12 text-robocell-orange animate-spin-slow" />
        </div>
        <div className="absolute bottom-40 left-20 animate-float" style={{ animationDelay: '2s' }}>
          <Zap className="h-6 w-6 text-electric-yellow" />
        </div>
      </div>

      <div className="max-w-7xl mx-auto text-center relative z-10">
        {/* Main Logo Display */}
        <div className="mb-12">
          <div className="inline-flex items-center justify-center">
            {/* Large RoboCell Logo */}
            <div className="relative group">
              <div className="w-32 h-32 md:w-40 md:h-40 rounded-full border-4 border-gray-600 bg-gradient-to-br from-robocell-yellow via-robocell-orange to-electric-amber flex items-center justify-center group-hover:scale-110 transition-all duration-500 shadow-2xl">
                <Bot className="h-16 w-16 md:h-20 md:w-20 text-white" />
                <Zap className="absolute h-8 w-8 md:h-10 md:w-10 text-white top-4 right-4 opacity-90" />
              </div>
              
              {/* Animated Ring */}
              <div className="absolute inset-0 w-32 h-32 md:w-40 md:h-40 rounded-full border-2 border-robocell-yellow opacity-0 group-hover:opacity-60 group-hover:animate-ping" />
              
              {/* Glow Effect */}
              <div className="absolute inset-0 w-32 h-32 md:w-40 md:h-40 rounded-full bg-gradient-to-br from-robocell-yellow to-robocell-orange opacity-20 blur-xl group-hover:opacity-40 transition-opacity duration-500" />
            </div>
          </div>
        </div>

        {/* Logo Description */}
        <div className="max-w-4xl mx-auto">
          <h2 className="font-tech text-3xl md:text-5xl font-bold text-white mb-6">
            The <span className="bg-gradient-to-r from-robocell-yellow to-robocell-orange bg-clip-text text-transparent">RoboCell</span> Identity
          </h2>
          
          <p className="text-lg md:text-xl text-gray-300 leading-relaxed mb-8">
            Our logo represents the perfect fusion of human creativity and robotic precision. 
            The lightning bolt symbolizes the spark of innovation that drives every project, 
            while the robot head embodies our technical expertise and futuristic vision. ⚡🤖
          </p>

          {/* Logo Elements Breakdown */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
            <div className="card text-center group">
              <div className="w-16 h-16 rounded-full bg-gradient-to-br from-robocell-yellow to-robocell-orange flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                <Bot className="h-8 w-8 text-white" />
              </div>
              <h3 className="font-tech text-xl font-bold text-white mb-2">Robot Head</h3>
              <p className="text-gray-300 text-sm">
                Represents our technical prowess and the future of automation
              </p>
            </div>

            <div className="card text-center group">
              <div className="w-16 h-16 rounded-full bg-gradient-to-br from-electric-yellow to-robocell-orange flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                <Zap className="h-8 w-8 text-white" />
              </div>
              <h3 className="font-tech text-xl font-bold text-white mb-2">Lightning Bolt</h3>
              <p className="text-gray-300 text-sm">
                The spark of innovation and energy that powers our creativity
              </p>
            </div>

            <div className="card text-center group">
              <div className="w-16 h-16 rounded-full border-2 border-robocell-yellow flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-robocell-yellow to-robocell-orange" />
              </div>
              <h3 className="font-tech text-xl font-bold text-white mb-2">Circular Unity</h3>
              <p className="text-gray-300 text-sm">
                The bond that unites our diverse team in pursuit of excellence
              </p>
            </div>
          </div>

          {/* Brand Colors */}
          <div className="mt-16">
            <h3 className="font-tech text-2xl font-bold text-white mb-8">Our Brand Colors</h3>
            <div className="flex flex-wrap justify-center gap-6">
              <div className="text-center">
                <div className="w-16 h-16 rounded-lg bg-robocell-yellow mx-auto mb-2 shadow-lg"></div>
                <p className="text-sm text-gray-300 font-mono">#FBBF24</p>
                <p className="text-xs text-gray-400">Energy Yellow</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 rounded-lg bg-robocell-orange mx-auto mb-2 shadow-lg"></div>
                <p className="text-sm text-gray-300 font-mono">#F97316</p>
                <p className="text-xs text-gray-400">Innovation Orange</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 rounded-lg bg-electric-amber mx-auto mb-2 shadow-lg"></div>
                <p className="text-sm text-gray-300 font-mono">#F59E0B</p>
                <p className="text-xs text-gray-400">Tech Amber</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 rounded-lg bg-dark-800 border border-gray-600 mx-auto mb-2 shadow-lg"></div>
                <p className="text-sm text-gray-300 font-mono">#1F2937</p>
                <p className="text-xs text-gray-400">Deep Space</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default LogoShowcase
